<!-- 文件路径: /Users/<USER>/Downloads/augment-vip-main/README.md -->
### 信息：使用 Python 版本可获得更多功能
你可以一键使用，或前往 python 分支
```bash
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/python/install.sh -o install.sh && chmod +x install.sh && ./install.sh
```

# Augment VIP

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Version](https://img.shields.io/badge/version-1.0.0-green.svg)

一个为 Augment VIP 用户提供的实用工具包，提供管理和清理 VS Code 数据库的工具。

## 🚀 功能

- **数据库清理**：从 VS Code 数据库中移除与 Augment 相关的条目
- **遥测 ID 修改**：为 VS Code 生成随机遥测 ID，以增强隐私
- **跨平台支持**：支持 macOS、Linux 和 Windows
- **安全操作**：在进行任何更改前创建备份
- **用户友好**：清晰的彩色输出和详细的状态消息

## 📋 依赖要求

- Bash shell 环境
- SQLite3
- curl（用于未来的更新）
- jq（用于配置解析）

## 💻 安装

### 一键安装

你可以使用 curl 一条命令安装：

```bash
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/install.sh -o install.sh && chmod +x install.sh && ./install.sh
```

这将会：
1. 下载安装脚本
2. 赋予其可执行权限
3. 在你当前目录下创建一个新的 `augment-vip` 目录
4. 运行安装程序，下载其他所需脚本
5. 提示你是否要运行数据库清理和遥测 ID 修改脚本

### 仓库安装

如果你更喜欢克隆整个仓库：

```bash
git clone https://github.com/azrilaiman2003/augment-vip.git
cd augment-vip
./scripts/install.sh
```

安装脚本将会：
1. 检查所需依赖
2. 下载其他所需脚本
3. 创建必要的项目目录
4. 设置默认配置
5. 使所有脚本可执行

### 自动化安装

你也可以通过选项自动运行清理和 ID 修改脚本。此方法适用于一键安装和仓库安装：

```bash
# 一键安装并启用所有功能
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/scripts/install.sh -o install.sh && chmod +x install.sh && ./install.sh --all

# 安装后，你将拥有一个 augment-vip 目录
# 进入该目录直接运行脚本：
cd augment-vip

# 或者如果你已经下载了脚本或克隆了仓库：

# 运行安装并清理数据库
./install.sh --clean
# 或
./scripts/install.sh --clean

# 运行安装并修改遥测 ID
./install.sh --modify-ids
# 或
./scripts/install.sh --modify-ids

# 运行安装、清理数据库并修改遥测 ID
./install.sh --all
# 或
./scripts/install.sh --all

# 显示帮助
./install.sh --help
# 或
./scripts/install.sh --help
```

### 手动安装

如果你更喜欢手动安装：

1. 克隆仓库：
   ```bash
   git clone https://github.com/azrilaiman2003/augment-vip.git
   cd augment-vip
   ```

2. 使脚本可执行：
   ```bash
   chmod +x scripts/*.sh
   ```

3. 创建所需目录：
   ```bash
   mkdir -p config logs data temp
   ```

## 🔧 用法

### 清理 VS Code 数据库

要从 VS Code 数据库中移除与 Augment 相关的条目：

```bash
./scripts/clean_code_db.sh
```

该脚本将会：
- 检测你的操作系统
- 查找 VS Code 数据库文件
- 为每个数据库创建备份
- 从数据库中移除包含 "augment" 的条目
- 报告结果

### 修改 VS Code 遥测 ID

要更改 VS Code 的 storage.json 文件中的遥测 ID：

```bash
./scripts/id_modifier.sh
```

该脚本将会：
- 定位 VS Code 的 storage.json 文件
- 生成一个 64 位十六进制字符串作为 machineId
- 生成一个 UUID v4 作为 devDeviceId
- 备份原始文件
- 用新值更新文件

## 📁 项目结构

```
augment-vip/
├── config/             # 配置文件
├── data/               # 数据存储
├── logs/               # 日志文件
├── scripts/            # 工具脚本
│   ├── clean_code_db.sh            # 数据库清理脚本
│   ├── id_modifier.sh              # 遥测 ID 修改脚本
│   └── install.sh                  # 安装脚本
├── temp/               # 临时文件
└── README.md           # 本文件
```

## 🔍 工作原理

数据库清理工具的工作流程：

1. **查找数据库位置**：根据你的操作系统自动检测 VS Code 数据库的正确路径。

2. **创建备份**：在进行任何更改前，为每个数据库文件创建备份。

3. **清理数据库**：使用 SQLite 命令从数据库中移除包含 "augment" 的条目。

4. **结果报告**：提供详细的操作反馈。

## 🛠️ 故障排除

### 常见问题

**缺少依赖**
```
[ERROR] sqlite3 is not installed
```
安装所需依赖：
- macOS: `brew install sqlite3 curl jq`
- Ubuntu/Debian: `sudo apt install sqlite3 curl jq`
- Fedora/RHEL: `sudo dnf install sqlite3 curl jq`
- Windows (使用 Chocolatey): `choco install sqlite curl jq`

**权限被拒绝**
```
[ERROR] Permission denied
```
确保脚本具有可执行权限：
```bash
chmod +x scripts/*.sh
```

**未找到数据库**
```
[WARNING] No database files found
```
如果你尚未在系统上使用 VS Code，或其安装在非标准位置，可能会出现此情况。

## 🤝 贡献

欢迎贡献！请随时提交 Pull Request。

1. Fork 本仓库
2. 创建你的功能分支（`git checkout -b feature/amazing-feature`）
3. 提交你的更改（`git commit -m 'Add some amazing feature'`）
4. 推送到分支（`git push origin feature/amazing-feature`）
5. 打开 Pull Request

## 📜 许可证

本项目采用 MIT 许可证，详情请参见 LICENSE 文件。

## 📞 联系方式

Azril Aiman - <EMAIL>

项目链接: [https://github.com/azrilaiman2003/augment-vip](https://github.com/azrilaiman2003/augment-vip)

---

由 [Azril Aiman](https://github.com/azrilaiman2003) ❤️ 制作
