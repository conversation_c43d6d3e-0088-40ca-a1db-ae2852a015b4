"""
系统管理 API 端点
提供系统配置、监控、管理等功能

作者: ZHT开发团队
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_superuser

router = APIRouter()


@router.get("/system-info")
async def get_system_info(
    current_user: dict = Depends(get_current_superuser)
):
    """
    获取系统信息 (需要超级用户权限)
    """
    return {
        "message": "获取系统信息成功",
        "data": {
            "version": "1.0.0",
            "environment": "development",
            "database": "connected",
            "redis": "connected",
            "ollama": "connected"
        }
    }


@router.get("/health-check")
async def admin_health_check(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_superuser)
):
    """
    管理员健康检查
    """
    return {
        "message": "系统健康检查完成",
        "data": {
            "status": "healthy",
            "services": {
                "database": "ok",
                "redis": "ok",
                "ollama": "ok"
            }
        }
    }
