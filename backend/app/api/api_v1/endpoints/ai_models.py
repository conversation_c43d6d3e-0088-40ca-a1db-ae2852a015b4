"""
AI模型集成 API 端点
提供AI模型调用、配置、管理等功能

作者: ZHT开发团队
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_active_user

router = APIRouter()


@router.get("/models")
async def get_available_models(
    current_user: dict = Depends(get_current_active_user)
):
    """
    获取可用的AI模型列表
    """
    return {
        "message": "获取AI模型列表成功",
        "data": [
            {"name": "llama3.2", "type": "llm", "provider": "ollama"},
            {"name": "qwen2", "type": "llm", "provider": "ollama"},
            {"name": "nomic-embed-text", "type": "embedding", "provider": "ollama"}
        ]
    }


@router.post("/chat")
async def chat_with_ai(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    与AI模型对话
    """
    return {
        "message": "AI对话功能开发中",
        "data": None
    }
