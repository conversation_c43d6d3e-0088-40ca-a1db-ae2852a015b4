"""
用户认证API端点 (简化版本)
提供基础的认证功能

作者: ZHT开发团队
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_active_user

router = APIRouter()


@router.post("/login")
async def register(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserRegister,
    request: Request
) -> Any:
    """
    用户注册

    - **username**: 用户名 (3-50字符，字母数字下划线)
    - **email**: 邮箱地址
    - **password**: 密码 (至少8位，包含字母和数字)
    - **full_name**: 真实姓名 (可选)
    - **phone**: 手机号码 (可选)
    """
    try:
        # 检查用户名是否已存在
        user_service = UserService(db)
        existing_user = await user_service.get_by_username(user_in.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )

        # 检查邮箱是否已存在
        existing_email = await user_service.get_by_email(user_in.email)
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱地址已被注册"
            )

        # 创建新用户
        user = await user_service.create_user(user_in)

        # 发送验证邮件 (异步)
        if settings.ENVIRONMENT != "testing":
            try:
                await send_verification_email(user.email, user.username)
                logger.info(f"验证邮件已发送至: {user.email}")
            except Exception as e:
                logger.warning(f"发送验证邮件失败: {e}")

        # 记录注册日志
        auth_service = AuthService(db)
        await auth_service.create_login_history(
            user_id=user.id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            login_type="register",
            status="success"
        )

        logger.info(f"新用户注册成功: {user.username} ({user.email})")
        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试"
        )


@router.post("/login", response_model=Token, summary="用户登录")
async def login(
    *,
    db: AsyncSession = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request
) -> Any:
    """
    用户登录

    使用用户名/邮箱和密码登录，返回访问令牌
    """
    try:
        auth_service = AuthService(db)
        user_service = UserService(db)

        # 验证用户凭据
        user = await auth_service.authenticate_user(
            username=form_data.username,
            password=form_data.password
        )

        if not user:
            # 记录失败的登录尝试
            await auth_service.create_login_history(
                user_id=None,
                ip_address=request.client.host,
                user_agent=request.headers.get("user-agent"),
                login_type="web",
                status="failed",
                failure_reason="invalid_credentials"
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查用户状态
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账户已被禁用"
            )

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        # 创建刷新令牌
        refresh_token = create_refresh_token(data={"sub": str(user.id)})

        # 更新用户登录信息
        await user_service.update_last_login(
            user_id=user.id,
            ip_address=request.client.host
        )

        # 记录成功的登录
        await auth_service.create_login_history(
            user_id=user.id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            login_type="web",
            status="success"
        )

        logger.info(f"用户登录成功: {user.username}")

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试"
        )


@router.post("/refresh", response_model=Token, summary="刷新令牌")
async def refresh_token(
    *,
    db: AsyncSession = Depends(get_db),
    token_data: TokenRefresh
) -> Any:
    """
    使用刷新令牌获取新的访问令牌
    """
    try:
        # 验证刷新令牌
        payload = verify_token(token_data.refresh_token)
        user_id = payload.get("sub")

        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )

        # 获取用户信息
        user_service = UserService(db)
        user = await user_service.get_by_id(int(user_id))

        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )

        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "refresh_token": token_data.refresh_token,  # 刷新令牌保持不变
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌刷新失败"
        )


@router.post("/logout", summary="用户登出")
async def logout(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    request: Request
) -> Any:
    """
    用户登出

    注意: 由于JWT是无状态的，实际的令牌失效需要在客户端处理
    这里主要记录登出日志
    """
    try:
        auth_service = AuthService(db)

        # 记录登出日志
        await auth_service.create_login_history(
            user_id=current_user.id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent"),
            login_type="web",
            status="logout"
        )

        logger.info(f"用户登出: {current_user.username}")

        return {"message": "登出成功"}

    except Exception as e:
        logger.error(f"用户登出失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败"
        )


@router.post("/password-reset-request", summary="请求密码重置")
async def password_reset_request(
    *,
    db: AsyncSession = Depends(get_db),
    reset_request: PasswordResetRequest
) -> Any:
    """
    请求密码重置

    发送密码重置邮件到用户邮箱
    """
    try:
        user_service = UserService(db)
        user = await user_service.get_by_email(reset_request.email)

        if not user:
            # 为了安全，即使邮箱不存在也返回成功消息
            return {"message": "如果邮箱存在，重置链接已发送"}

        # 生成重置令牌
        reset_token = create_access_token(
            data={"sub": str(user.id), "type": "password_reset"},
            expires_delta=timedelta(hours=1)  # 1小时有效期
        )

        # 发送重置邮件
        if settings.ENVIRONMENT != "testing":
            try:
                await send_password_reset_email(user.email, user.username, reset_token)
                logger.info(f"密码重置邮件已发送至: {user.email}")
            except Exception as e:
                logger.warning(f"发送密码重置邮件失败: {e}")

        return {"message": "如果邮箱存在，重置链接已发送"}

    except Exception as e:
        logger.error(f"密码重置请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="请求失败，请稍后重试"
        )


@router.post("/password-reset", summary="重置密码")
async def password_reset(
    *,
    db: AsyncSession = Depends(get_db),
    reset_data: PasswordReset
) -> Any:
    """
    重置密码

    使用重置令牌设置新密码
    """
    try:
        # 验证重置令牌
        payload = verify_token(reset_data.token)
        user_id = payload.get("sub")
        token_type = payload.get("type")

        if not user_id or token_type != "password_reset":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的重置令牌"
            )

        # 获取用户
        user_service = UserService(db)
        user = await user_service.get_by_id(int(user_id))

        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户不存在"
            )

        # 更新密码
        await user_service.update_password(user.id, reset_data.new_password)

        logger.info(f"用户密码重置成功: {user.username}")

        return {"message": "密码重置成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码重置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="密码重置失败"
        )


@router.get("/me", response_model=UserResponse, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    获取当前登录用户的详细信息
    """
    return current_user


@router.post("/verify-email/{token}", summary="验证邮箱")
async def verify_email(
    *,
    db: AsyncSession = Depends(get_db),
    token: str
) -> Any:
    """
    验证用户邮箱地址
    """
    try:
        # 验证令牌
        payload = verify_token(token)
        user_id = payload.get("sub")
        token_type = payload.get("type")

        if not user_id or token_type != "email_verification":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的验证令牌"
            )

        # 更新用户验证状态
        user_service = UserService(db)
        user = await user_service.verify_email(int(user_id))

        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户不存在"
            )

        logger.info(f"用户邮箱验证成功: {user.email}")

        return {"message": "邮箱验证成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮箱验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证失败"
        )
