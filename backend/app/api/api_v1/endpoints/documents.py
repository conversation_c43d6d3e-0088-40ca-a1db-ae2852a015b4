"""
文档管理 API 端点
提供文档上传、处理、管理等功能

作者: ZHT开发团队
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_active_user

router = APIRouter()


@router.get("/")
async def get_documents(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    获取文档列表
    """
    return {
        "message": "获取文档列表成功",
        "data": []
    }


@router.post("/upload")
async def upload_document(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    上传文档
    """
    return {
        "message": "文档上传功能开发中",
        "data": None
    }
