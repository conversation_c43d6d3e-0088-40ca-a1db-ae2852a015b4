"""
用户管理 API 端点
提供用户注册、登录、信息管理等功能

作者: ZHT开发团队
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, get_current_user, get_current_active_user

router = APIRouter()


@router.get("/me")
async def get_current_user_info(
    current_user: dict = Depends(get_current_active_user)
):
    """
    获取当前用户信息
    """
    return {
        "message": "获取用户信息成功",
        "data": current_user
    }


@router.get("/")
async def get_users(
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    """
    获取用户列表 (需要管理员权限)
    """
    return {
        "message": "获取用户列表成功",
        "data": [current_user]  # 临时返回当前用户
    }
