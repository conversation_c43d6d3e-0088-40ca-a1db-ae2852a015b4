"""
API 依赖注入模块
提供数据库会话、用户认证等依赖注入功能

作者: ZHT开发团队
"""

from typing import Generator, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db as get_async_db, get_sync_db
from app.core.config import settings

# 安全相关
security = HTTPBearer()


# 数据库依赖
async def get_db() -> AsyncSession:
    """
    获取异步数据库会话
    """
    async for session in get_async_db():
        yield session


def get_sync_database() -> Generator[Session, None, None]:
    """
    获取同步数据库会话 (用于特殊场景)
    """
    yield from get_sync_db()


# 用户认证依赖 (简化版本，后续完善)
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """
    获取当前用户信息
    简化版本，后续需要完善JWT验证逻辑
    """
    # TODO: 实现JWT token验证
    # 目前返回模拟用户信息
    return {
        "id": 1,
        "email": "<EMAIL>",
        "is_active": True,
        "is_superuser": True
    }


async def get_current_active_user(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """
    获取当前活跃用户
    """
    if not current_user.get("is_active"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user


async def get_current_superuser(
    current_user: dict = Depends(get_current_user)
) -> dict:
    """
    获取当前超级用户
    """
    if not current_user.get("is_superuser"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级用户权限"
        )
    return current_user


# 可选的认证依赖 (允许匿名访问)
async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[dict]:
    """
    可选的用户认证，允许匿名访问
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None


# 配置依赖
def get_settings():
    """
    获取应用配置
    """
    return settings
