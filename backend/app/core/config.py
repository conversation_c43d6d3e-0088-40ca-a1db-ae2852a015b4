"""
应用配置管理模块
使用 Pydantic Settings 管理应用配置，支持环境变量和配置文件

配置优先级：
1. 环境变量
2. .env 文件
3. 默认值

作者: ZHT开发团队
"""

from typing import List, Optional, Union
from pydantic import validator, AnyHttpUrl
from pydantic_settings import BaseSettings
import secrets
import os


class Settings(BaseSettings):
    """
    应用配置类
    所有配置项都可以通过环境变量覆盖
    """

    # 基础应用配置
    PROJECT_NAME: str = "中小型企业智能应用系统"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    ENVIRONMENT: str = "development"  # development, testing, production

    # 安全配置
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"

    # 数据库配置
    DATABASE_URL: Optional[str] = None
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "zht_user"
    POSTGRES_PASSWORD: str = "zht_password"
    POSTGRES_DB: str = "zht_sys"
    POSTGRES_PORT: int = 5432

    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        """
        构建数据库连接URL
        如果没有提供完整的DATABASE_URL，则从单独的配置项构建
        """
        if isinstance(v, str):
            return v
        return (
            f"postgresql://{values.get('POSTGRES_USER')}:"
            f"{values.get('POSTGRES_PASSWORD')}@"
            f"{values.get('POSTGRES_SERVER')}:"
            f"{values.get('POSTGRES_PORT')}/"
            f"{values.get('POSTGRES_DB')}"
        )

    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # Celery配置
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None

    @validator("CELERY_BROKER_URL", pre=True)
    def assemble_celery_broker(cls, v: Optional[str], values: dict) -> str:
        """构建Celery broker URL"""
        if isinstance(v, str):
            return v
        return values.get("REDIS_URL", "redis://localhost:6379/0")

    @validator("CELERY_RESULT_BACKEND", pre=True)
    def assemble_celery_backend(cls, v: Optional[str], values: dict) -> str:
        """构建Celery result backend URL"""
        if isinstance(v, str):
            return v
        return values.get("REDIS_URL", "redis://localhost:6379/0")

    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",  # React开发服务器
        "http://localhost:8080",  # 备用前端端口
        "http://127.0.0.1:3000",
    ]

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """处理CORS origins配置"""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 可信主机配置
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1", "0.0.0.0"]

    # 文件上传配置
    UPLOAD_DIR: str = "./uploads"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB

    @validator("MAX_FILE_SIZE", pre=True)
    def parse_file_size(cls, v):
        """解析文件大小，支持字符串格式如 '50MB'"""
        if isinstance(v, str):
            v = v.upper()
            if v.endswith('MB'):
                return int(v[:-2]) * 1024 * 1024
            elif v.endswith('KB'):
                return int(v[:-2]) * 1024
            elif v.endswith('GB'):
                return int(v[:-2]) * 1024 * 1024 * 1024
            else:
                return int(v)
        return v

    ALLOWED_FILE_TYPES: List[str] = [
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword",
        "text/plain",
        "text/markdown",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel"
    ]

    # AI模型配置
    # Ollama配置 (开发环境)
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_LLM_MODEL: str = "llama3.2"
    OLLAMA_EMBED_MODEL: str = "nomic-embed-text"

    # 阿里云百炼配置 (生产环境)
    DASHSCOPE_API_KEY: Optional[str] = None
    DASHSCOPE_LLM_MODEL: str = "qwen-turbo"
    DASHSCOPE_EMBED_MODEL: str = "text-embedding-v1"

    # 阿里云OSS配置 (生产环境)
    ALICLOUD_ACCESS_KEY_ID: Optional[str] = None
    ALICLOUD_ACCESS_KEY_SECRET: Optional[str] = None
    ALICLOUD_OSS_ENDPOINT: Optional[str] = None
    ALICLOUD_OSS_BUCKET: Optional[str] = None

    # RAG配置
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    TOP_K_RETRIEVAL: int = 5
    SIMILARITY_THRESHOLD: float = 0.7

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090

    # 邮件配置 (可选)
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None

    # 超级用户配置
    FIRST_SUPERUSER_EMAIL: str = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "admin123456"

    class Config:
        """Pydantic配置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """
    获取应用配置实例
    用于依赖注入
    """
    return settings


# 环境特定配置
def is_development() -> bool:
    """判断是否为开发环境"""
    return settings.ENVIRONMENT == "development"


def is_production() -> bool:
    """判断是否为生产环境"""
    return settings.ENVIRONMENT == "production"


def is_testing() -> bool:
    """判断是否为测试环境"""
    return settings.ENVIRONMENT == "testing"


# 配置验证
def validate_config():
    """
    验证关键配置项
    在应用启动时调用
    """
    errors = []

    # 检查生产环境必需配置
    if is_production():
        if not settings.DASHSCOPE_API_KEY:
            errors.append("生产环境必须配置 DASHSCOPE_API_KEY")

        if not settings.ALICLOUD_ACCESS_KEY_ID:
            errors.append("生产环境必须配置 ALICLOUD_ACCESS_KEY_ID")

        if settings.SECRET_KEY == "your-secret-key-change-in-production":
            errors.append("生产环境必须修改默认的 SECRET_KEY")

    # 检查数据库配置
    if not settings.DATABASE_URL:
        errors.append("必须配置数据库连接 DATABASE_URL")

    if errors:
        raise ValueError(f"配置验证失败: {'; '.join(errors)}")


if __name__ == "__main__":
    # 测试配置
    print("当前配置:")
    print(f"环境: {settings.ENVIRONMENT}")
    print(f"数据库: {settings.DATABASE_URL}")
    print(f"Redis: {settings.REDIS_URL}")
    print(f"CORS Origins: {settings.BACKEND_CORS_ORIGINS}")

    try:
        validate_config()
        print("✅ 配置验证通过")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
