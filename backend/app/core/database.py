"""
数据库配置和连接管理模块
使用 SQLAlchemy 2.0+ 异步ORM，支持 PostgreSQL + pgvector

主要功能：
1. 数据库连接池管理
2. 异步会话管理
3. 向量数据库支持
4. 事务管理

作者: ZHT开发团队
"""

from typing import AsyncGenerator, Optional, Generator
from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# 创建同步数据库引擎 (用于Alembic迁移)
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,  # 连接前检查连接是否有效
    pool_recycle=300,    # 连接回收时间 (秒)
    pool_size=5,         # 连接池大小
    max_overflow=10,     # 最大溢出连接数
    echo=settings.ENVIRONMENT == "development",  # 开发环境显示SQL
)

# 创建异步数据库引擎
async_database_url = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
async_engine = create_async_engine(
    async_database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=5,
    max_overflow=10,
    echo=settings.ENVIRONMENT == "development",
)

# 创建同步会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()

# 元数据对象，用于表结构管理
metadata = MetaData()


class DatabaseManager:
    """
    数据库管理器
    提供数据库连接、事务管理等功能
    """

    def __init__(self):
        self.engine = async_engine
        self.session_factory = AsyncSessionLocal

    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        获取异步数据库会话
        用于依赖注入
        """
        async with self.session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(f"数据库会话错误: {e}")
                raise
            finally:
                await session.close()

    async def init_db(self) -> None:
        """
        初始化数据库
        创建表结构和必要的扩展
        """
        try:
            async with self.engine.begin() as conn:
                # 启用 pgvector 扩展
                await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
                logger.info("✅ pgvector 扩展已启用")

                # 创建所有表 (仅开发环境)
                if settings.ENVIRONMENT == "development":
                    await conn.run_sync(Base.metadata.create_all)
                    logger.info("✅ 数据库表结构已创建")

        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            raise

    async def check_connection(self) -> bool:
        """
        检查数据库连接状态
        """
        try:
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False

    async def close(self) -> None:
        """
        关闭数据库连接
        """
        await self.engine.dispose()
        logger.info("数据库连接已关闭")


# 创建全局数据库管理器实例
db_manager = DatabaseManager()


# 依赖注入函数
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话的依赖注入函数
    在 FastAPI 路由中使用: db: AsyncSession = Depends(get_db)
    """
    async for session in db_manager.get_session():
        yield session


def get_sync_db() -> Generator[Session, None, None]:
    """
    获取同步数据库会话 (用于特殊场景)
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# 数据库工具函数
async def execute_raw_sql(sql: str, params: Optional[dict] = None) -> any:
    """
    执行原生SQL查询

    Args:
        sql: SQL查询语句
        params: 查询参数

    Returns:
        查询结果
    """
    async with AsyncSessionLocal() as session:
        try:
            result = await session.execute(text(sql), params or {})
            await session.commit()
            return result
        except Exception as e:
            await session.rollback()
            logger.error(f"SQL执行失败: {e}")
            raise


async def check_table_exists(table_name: str) -> bool:
    """
    检查表是否存在

    Args:
        table_name: 表名

    Returns:
        表是否存在
    """
    sql = """
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = :table_name
    );
    """
    try:
        result = await execute_raw_sql(sql, {"table_name": table_name})
        return result.scalar()
    except Exception:
        return False


async def get_table_info(table_name: str) -> dict:
    """
    获取表结构信息

    Args:
        table_name: 表名

    Returns:
        表结构信息
    """
    sql = """
    SELECT
        column_name,
        data_type,
        is_nullable,
        column_default
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = :table_name
    ORDER BY ordinal_position;
    """
    try:
        result = await execute_raw_sql(sql, {"table_name": table_name})
        columns = []
        for row in result:
            columns.append({
                "name": row.column_name,
                "type": row.data_type,
                "nullable": row.is_nullable == "YES",
                "default": row.column_default
            })
        return {"table": table_name, "columns": columns}
    except Exception as e:
        logger.error(f"获取表信息失败: {e}")
        return {}


# 向量数据库相关函数
async def create_vector_index(table_name: str, column_name: str, index_type: str = "ivfflat") -> bool:
    """
    创建向量索引

    Args:
        table_name: 表名
        column_name: 向量列名
        index_type: 索引类型 (ivfflat, hnsw)

    Returns:
        是否创建成功
    """
    index_name = f"idx_{table_name}_{column_name}_{index_type}"

    if index_type == "ivfflat":
        sql = f"""
        CREATE INDEX IF NOT EXISTS {index_name}
        ON {table_name}
        USING ivfflat ({column_name} vector_cosine_ops)
        WITH (lists = 100);
        """
    elif index_type == "hnsw":
        sql = f"""
        CREATE INDEX IF NOT EXISTS {index_name}
        ON {table_name}
        USING hnsw ({column_name} vector_cosine_ops)
        WITH (m = 16, ef_construction = 64);
        """
    else:
        logger.error(f"不支持的索引类型: {index_type}")
        return False

    try:
        await execute_raw_sql(sql)
        logger.info(f"✅ 向量索引 {index_name} 创建成功")
        return True
    except Exception as e:
        logger.error(f"❌ 向量索引创建失败: {e}")
        return False


if __name__ == "__main__":
    import asyncio

    async def test_connection():
        """测试数据库连接"""
        print("测试数据库连接...")

        # 检查连接
        is_connected = await db_manager.check_connection()
        print(f"连接状态: {'✅ 正常' if is_connected else '❌ 失败'}")

        if is_connected:
            # 初始化数据库
            await db_manager.init_db()
            print("✅ 数据库初始化完成")

        await db_manager.close()

    # 运行测试
    asyncio.run(test_connection())
